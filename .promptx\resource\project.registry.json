{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-11T13:53:34.276Z", "updatedAt": "2025-08-11T13:53:34.317Z", "resourceCount": 28}, "resources": [{"id": "literary-cinematic-fusion", "source": "project", "protocol": "execution", "name": "Literary Cinematic Fusion 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/神棍/execution/literary-cinematic-fusion.execution.md", "metadata": {"createdAt": "2025-08-11T13:53:34.303Z", "updatedAt": "2025-08-11T13:53:34.303Z", "scannedAt": "2025-08-11T13:53:34.303Z", "path": "domain/神棍/execution/literary-cinematic-fusion.execution.md"}}, {"id": "novel-writing-mastery", "source": "project", "protocol": "execution", "name": "Novel Writing Mastery 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/神棍/execution/novel-writing-mastery.execution.md", "metadata": {"createdAt": "2025-08-11T13:53:34.304Z", "updatedAt": "2025-08-11T13:53:34.304Z", "scannedAt": "2025-08-11T13:53:34.304Z", "path": "domain/神棍/execution/novel-writing-mastery.execution.md"}}, {"id": "chinese-fantasy-mastery", "source": "project", "protocol": "knowledge", "name": "Chinese Fantasy Mastery 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/神棍/knowledge/chinese-fantasy-mastery.knowledge.md", "metadata": {"createdAt": "2025-08-11T13:53:34.305Z", "updatedAt": "2025-08-11T13:53:34.305Z", "scannedAt": "2025-08-11T13:53:34.305Z", "path": "domain/神棍/knowledge/chinese-fantasy-mastery.knowledge.md"}}, {"id": "narrative-fusion-techniques", "source": "project", "protocol": "knowledge", "name": "Narrative Fusion Techniques 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/神棍/knowledge/narrative-fusion-techniques.knowledge.md", "metadata": {"createdAt": "2025-08-11T13:53:34.306Z", "updatedAt": "2025-08-11T13:53:34.306Z", "scannedAt": "2025-08-11T13:53:34.306Z", "path": "domain/神棍/knowledge/narrative-fusion-techniques.knowledge.md"}}, {"id": "novel-master-expertise", "source": "project", "protocol": "knowledge", "name": "Novel Master Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/神棍/knowledge/novel-master-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-11T13:53:34.306Z", "updatedAt": "2025-08-11T13:53:34.306Z", "scannedAt": "2025-08-11T13:53:34.306Z", "path": "domain/神棍/knowledge/novel-master-expertise.knowledge.md"}}, {"id": "literary-imagination", "source": "project", "protocol": "thought", "name": "Literary Imagination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/神棍/thought/literary-imagination.thought.md", "metadata": {"createdAt": "2025-08-11T13:53:34.307Z", "updatedAt": "2025-08-11T13:53:34.307Z", "scannedAt": "2025-08-11T13:53:34.307Z", "path": "domain/神棍/thought/literary-imagination.thought.md"}}, {"id": "挽棠卿", "source": "project", "protocol": "role", "name": "挽棠卿 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/挽棠卿/挽棠卿.role.md", "metadata": {"createdAt": "2025-08-11T13:53:34.293Z", "updatedAt": "2025-08-11T13:53:34.293Z", "scannedAt": "2025-08-11T13:53:34.293Z", "path": "domain/挽棠卿/挽棠卿.role.md"}}, {"id": "content-quality-control", "source": "project", "protocol": "execution", "name": "Content Quality Control 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/文稿审核/execution/content-quality-control.execution.md", "metadata": {"createdAt": "2025-08-11T13:53:34.294Z", "updatedAt": "2025-08-11T13:53:34.294Z", "scannedAt": "2025-08-11T13:53:34.294Z", "path": "domain/文稿审核/execution/content-quality-control.execution.md"}}, {"id": "editorial-standards", "source": "project", "protocol": "execution", "name": "Editorial Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/文稿审核/execution/editorial-standards.execution.md", "metadata": {"createdAt": "2025-08-11T13:53:34.295Z", "updatedAt": "2025-08-11T13:53:34.295Z", "scannedAt": "2025-08-11T13:53:34.295Z", "path": "domain/文稿审核/execution/editorial-standards.execution.md"}}, {"id": "manuscript-review-process", "source": "project", "protocol": "execution", "name": "Manuscript Review Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/文稿审核/execution/manuscript-review-process.execution.md", "metadata": {"createdAt": "2025-08-11T13:53:34.296Z", "updatedAt": "2025-08-11T13:53:34.296Z", "scannedAt": "2025-08-11T13:53:34.296Z", "path": "domain/文稿审核/execution/manuscript-review-process.execution.md"}}, {"id": "chinese-literature-standards", "source": "project", "protocol": "knowledge", "name": "Chinese Literature Standards 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/文稿审核/knowledge/chinese-literature-standards.knowledge.md", "metadata": {"createdAt": "2025-08-11T13:53:34.297Z", "updatedAt": "2025-08-11T13:53:34.297Z", "scannedAt": "2025-08-11T13:53:34.297Z", "path": "domain/文稿审核/knowledge/chinese-literature-standards.knowledge.md"}}, {"id": "fantasy-genre-conventions", "source": "project", "protocol": "knowledge", "name": "Fantasy Genre Conventions 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/文稿审核/knowledge/fantasy-genre-conventions.knowledge.md", "metadata": {"createdAt": "2025-08-11T13:53:34.297Z", "updatedAt": "2025-08-11T13:53:34.297Z", "scannedAt": "2025-08-11T13:53:34.297Z", "path": "domain/文稿审核/knowledge/fantasy-genre-conventions.knowledge.md"}}, {"id": "manuscript-review-expertise", "source": "project", "protocol": "knowledge", "name": "Manuscript Review Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/文稿审核/knowledge/manuscript-review-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-11T13:53:34.299Z", "updatedAt": "2025-08-11T13:53:34.299Z", "scannedAt": "2025-08-11T13:53:34.298Z", "path": "domain/文稿审核/knowledge/manuscript-review-expertise.knowledge.md"}}, {"id": "critical-thinking", "source": "project", "protocol": "thought", "name": "Critical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/文稿审核/thought/critical-thinking.thought.md", "metadata": {"createdAt": "2025-08-11T13:53:34.300Z", "updatedAt": "2025-08-11T13:53:34.300Z", "scannedAt": "2025-08-11T13:53:34.300Z", "path": "domain/文稿审核/thought/critical-thinking.thought.md"}}, {"id": "detail-oriented", "source": "project", "protocol": "thought", "name": "Detail Oriented 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/文稿审核/thought/detail-oriented.thought.md", "metadata": {"createdAt": "2025-08-11T13:53:34.300Z", "updatedAt": "2025-08-11T13:53:34.300Z", "scannedAt": "2025-08-11T13:53:34.300Z", "path": "domain/文稿审核/thought/detail-oriented.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/文稿审核/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-08-11T13:53:34.301Z", "updatedAt": "2025-08-11T13:53:34.301Z", "scannedAt": "2025-08-11T13:53:34.301Z", "path": "domain/文稿审核/thought/quality-control.thought.md"}}, {"id": "文稿审核", "source": "project", "protocol": "role", "name": "文稿审核 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/文稿审核/文稿审核.role.md", "metadata": {"createdAt": "2025-08-11T13:53:34.302Z", "updatedAt": "2025-08-11T13:53:34.302Z", "scannedAt": "2025-08-11T13:53:34.302Z", "path": "domain/文稿审核/文稿审核.role.md"}}, {"id": "神棍", "source": "project", "protocol": "role", "name": "神棍 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/神棍/神棍.role.md", "metadata": {"createdAt": "2025-08-11T13:53:34.307Z", "updatedAt": "2025-08-11T13:53:34.307Z", "scannedAt": "2025-08-11T13:53:34.307Z", "path": "domain/神棍/神棍.role.md"}}, {"id": "content-interpretation", "source": "project", "protocol": "execution", "name": "Content Interpretation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/读者视角/execution/content-interpretation.execution.md", "metadata": {"createdAt": "2025-08-11T13:53:34.308Z", "updatedAt": "2025-08-11T13:53:34.308Z", "scannedAt": "2025-08-11T13:53:34.308Z", "path": "domain/读者视角/execution/content-interpretation.execution.md"}}, {"id": "emotional-evaluation", "source": "project", "protocol": "execution", "name": "Emotional Evaluation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/读者视角/execution/emotional-evaluation.execution.md", "metadata": {"createdAt": "2025-08-11T13:53:34.309Z", "updatedAt": "2025-08-11T13:53:34.309Z", "scannedAt": "2025-08-11T13:53:34.309Z", "path": "domain/读者视角/execution/emotional-evaluation.execution.md"}}, {"id": "reader-experience-analysis", "source": "project", "protocol": "execution", "name": "Reader Experience Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/读者视角/execution/reader-experience-analysis.execution.md", "metadata": {"createdAt": "2025-08-11T13:53:34.311Z", "updatedAt": "2025-08-11T13:53:34.311Z", "scannedAt": "2025-08-11T13:53:34.311Z", "path": "domain/读者视角/execution/reader-experience-analysis.execution.md"}}, {"id": "audience-analysis", "source": "project", "protocol": "knowledge", "name": "Audience Analysis 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/读者视角/knowledge/audience-analysis.knowledge.md", "metadata": {"createdAt": "2025-08-11T13:53:34.312Z", "updatedAt": "2025-08-11T13:53:34.312Z", "scannedAt": "2025-08-11T13:53:34.312Z", "path": "domain/读者视角/knowledge/audience-analysis.knowledge.md"}}, {"id": "reader-perspective-expertise", "source": "project", "protocol": "knowledge", "name": "Reader Perspective Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/读者视角/knowledge/reader-perspective-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-11T13:53:34.312Z", "updatedAt": "2025-08-11T13:53:34.312Z", "scannedAt": "2025-08-11T13:53:34.312Z", "path": "domain/读者视角/knowledge/reader-perspective-expertise.knowledge.md"}}, {"id": "reading-psychology", "source": "project", "protocol": "knowledge", "name": "Reading Psychology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/读者视角/knowledge/reading-psychology.knowledge.md", "metadata": {"createdAt": "2025-08-11T13:53:34.313Z", "updatedAt": "2025-08-11T13:53:34.313Z", "scannedAt": "2025-08-11T13:53:34.313Z", "path": "domain/读者视角/knowledge/reading-psychology.knowledge.md"}}, {"id": "emotional-resonance", "source": "project", "protocol": "thought", "name": "Emotional Resonance 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/读者视角/thought/emotional-resonance.thought.md", "metadata": {"createdAt": "2025-08-11T13:53:34.314Z", "updatedAt": "2025-08-11T13:53:34.314Z", "scannedAt": "2025-08-11T13:53:34.314Z", "path": "domain/读者视角/thought/emotional-resonance.thought.md"}}, {"id": "empathetic-reading", "source": "project", "protocol": "thought", "name": "Empathetic Reading 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/读者视角/thought/empathetic-reading.thought.md", "metadata": {"createdAt": "2025-08-11T13:53:34.315Z", "updatedAt": "2025-08-11T13:53:34.315Z", "scannedAt": "2025-08-11T13:53:34.315Z", "path": "domain/读者视角/thought/empathetic-reading.thought.md"}}, {"id": "reader-psychology", "source": "project", "protocol": "thought", "name": "Reader Psychology 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/读者视角/thought/reader-psychology.thought.md", "metadata": {"createdAt": "2025-08-11T13:53:34.316Z", "updatedAt": "2025-08-11T13:53:34.316Z", "scannedAt": "2025-08-11T13:53:34.316Z", "path": "domain/读者视角/thought/reader-psychology.thought.md"}}, {"id": "读者视角", "source": "project", "protocol": "role", "name": "读者视角 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/读者视角/读者视角.role.md", "metadata": {"createdAt": "2025-08-11T13:53:34.316Z", "updatedAt": "2025-08-11T13:53:34.316Z", "scannedAt": "2025-08-11T13:53:34.316Z", "path": "domain/读者视角/读者视角.role.md"}}], "stats": {"totalResources": 28, "byProtocol": {"execution": 8, "knowledge": 9, "thought": 7, "role": 4}, "bySource": {"project": 28}}}